{"model": {"vocab_size": 50000, "d_model": 768, "n_heads": 12, "n_layers": 12, "d_ff": 3072, "max_seq_len": 1024, "dropout": 0.1, "attention_dropout": 0.1, "activation": "gelu", "pre_norm": true, "use_bias": true, "gradient_checkpointing": false}, "training": {"batch_size": 8, "learning_rate": 0.0001, "weight_decay": 0.01, "num_epochs": 3, "max_steps": null, "lr_scheduler": "cosine", "warmup_steps": 2000, "warmup_ratio": 0.1, "min_lr_ratio": 0.1, "optimizer": "adamw", "beta1": 0.9, "beta2": 0.999, "eps": 1e-08, "max_grad_norm": 1.0, "gradient_accumulation_steps": 8, "use_amp": true, "amp_dtype": "float16", "save_steps": 2000, "save_total_limit": 3, "checkpoint_dir": "./checkpoints", "resume_from_checkpoint": null, "eval_steps": 1000, "eval_strategy": "steps", "logging_steps": 100, "log_level": "info", "report_to": ["tensorboard"], "dataloader_num_workers": 4, "dataloader_pin_memory": true, "dataloader_drop_last": true, "seed": 42, "compile_model": false}, "data": {"dataset_name": "wikitext", "dataset_config": "wikitext-103-raw-v1", "dataset_split": "train", "validation_split": "validation", "test_split": "test", "text_column": "text", "max_length": 1024, "min_length": 10, "stride": 512, "vocab_size": 50000, "special_tokens": ["<pad>", "<unk>", "<bos>", "<eos>"], "filter_empty_lines": true, "filter_short_sequences": true, "remove_duplicates": false, "pack_sequences": true, "packing_strategy": "greedy", "preprocessing_num_workers": 8, "cache_dir": null, "use_streaming": false}, "tokenizer": {"dataset_name": "wikitext", "dataset_config": "wikitext-103-raw-v1", "vocab_size": 50000, "max_samples": 100000, "save_path": "./tokenizer"}}