"""Byte Pair Encoding (BPE) tokenizer implementation from scratch."""

import re
import json
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set, Optional, Union
from tqdm import tqdm
import unicodedata

from .base_tokenizer import BaseTokenizer


class BPETokenizer(BaseTokenizer):
    """Byte Pair Encoding tokenizer implemented from scratch."""
    
    def __init__(self):
        super().__init__()
        self.merges: List[Tuple[str, str]] = []
        self.cache: Dict[str, List[str]] = {}
        
        # Regex pattern for pre-tokenization (similar to GPT-2)
        self.pat = re.compile(
            r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+""",
            re.IGNORECASE
        )
    
    def _get_stats(self, vocab: Dict[str, int]) -> Dict[Tuple[str, str], int]:
        """Get frequency of adjacent symbol pairs."""
        pairs = defaultdict(int)
        for word, freq in vocab.items():
            symbols = word.split()
            for i in range(len(symbols) - 1):
                pairs[(symbols[i], symbols[i + 1])] += freq
        return pairs
    
    def _merge_vocab(self, pair: Tuple[str, str], vocab: Dict[str, int]) -> Dict[str, int]:
        """Merge the most frequent pair in vocabulary."""
        bigram = re.escape(' '.join(pair))
        p = re.compile(r'(?<!\S)' + bigram + r'(?!\S)')
        
        new_vocab = {}
        for word in vocab:
            new_word = p.sub(''.join(pair), word)
            new_vocab[new_word] = vocab[word]
        return new_vocab
    
    def _get_word_tokens(self, text: str) -> List[str]:
        """Pre-tokenize text into words using regex pattern."""
        # Normalize unicode
        text = unicodedata.normalize('NFC', text)
        
        # Find all matches
        tokens = []
        for match in self.pat.finditer(text):
            token = match.group()
            # Convert to bytes and back to handle any character
            token_bytes = token.encode('utf-8')
            token = ''.join(chr(b) for b in token_bytes)
            tokens.append(token)
        
        return tokens
    
    def train(self, texts: List[str], vocab_size: int = 50000, 
              min_frequency: int = 2, verbose: bool = True) -> None:
        """Train BPE tokenizer on corpus."""
        if verbose:
            print("Training BPE tokenizer...")
        
        # Initialize special tokens
        self.vocab = {
            self.pad_token: self.pad_token_id,
            self.unk_token: self.unk_token_id,
            self.bos_token: self.bos_token_id,
            self.eos_token: self.eos_token_id
        }
        
        self.special_tokens = {
            self.pad_token: self.pad_token_id,
            self.unk_token: self.unk_token_id,
            self.bos_token: self.bos_token_id,
            self.eos_token: self.eos_token_id
        }
        
        # Collect word frequencies
        if verbose:
            print("Collecting word frequencies...")
        
        word_freqs = Counter()
        for text in tqdm(texts, desc="Processing texts", disable=not verbose):
            words = self._get_word_tokens(text)
            for word in words:
                word_freqs[word] += 1
        
        # Filter by minimum frequency
        word_freqs = {word: freq for word, freq in word_freqs.items() 
                     if freq >= min_frequency}
        
        if verbose:
            print(f"Found {len(word_freqs)} unique words")
        
        # Initialize vocabulary with character-level tokens
        vocab = {}
        for word, freq in word_freqs.items():
            # Split word into characters and add end-of-word marker
            word_tokens = list(word) + ['</w>']
            vocab[' '.join(word_tokens)] = freq
        
        # Get all unique characters
        all_chars = set()
        for word in vocab:
            all_chars.update(word.split())
        
        # Add character tokens to vocabulary
        for char in sorted(all_chars):
            if char not in self.vocab:
                self.vocab[char] = len(self.vocab)
        
        if verbose:
            print(f"Initial vocabulary size: {len(self.vocab)}")
        
        # Learn BPE merges
        num_merges = vocab_size - len(self.vocab)
        self.merges = []
        
        if verbose:
            print(f"Learning {num_merges} BPE merges...")
        
        for i in tqdm(range(num_merges), desc="Learning merges", disable=not verbose):
            pairs = self._get_stats(vocab)
            if not pairs:
                break
            
            # Find most frequent pair
            best_pair = max(pairs, key=pairs.get)
            
            # Merge the pair
            vocab = self._merge_vocab(best_pair, vocab)
            self.merges.append(best_pair)
            
            # Add merged token to vocabulary
            merged_token = ''.join(best_pair)
            if merged_token not in self.vocab:
                self.vocab[merged_token] = len(self.vocab)
        
        # Create inverse vocabulary
        self.inverse_vocab = {v: k for k, v in self.vocab.items()}
        
        if verbose:
            print(f"Final vocabulary size: {len(self.vocab)}")
            print(f"Learned {len(self.merges)} merges")
    
    def train_from_dataset(self, dataset_name: str, dataset_config: Optional[str] = None,
                          split: str = "train", text_column: str = "text",
                          vocab_size: int = 50000, max_samples: Optional[int] = None,
                          verbose: bool = True) -> None:
        """Train tokenizer from Hugging Face dataset."""
        try:
            from datasets import load_dataset
        except ImportError:
            raise ImportError("Please install datasets: pip install datasets")
        
        if verbose:
            print(f"Loading dataset: {dataset_name}")
        
        # Load dataset
        if dataset_config:
            dataset = load_dataset(dataset_name, dataset_config, split=split)
        else:
            dataset = load_dataset(dataset_name, split=split)
        
        # Extract texts
        texts = []
        max_samples = max_samples or len(dataset)
        
        for i, example in enumerate(tqdm(dataset, desc="Loading texts", disable=not verbose)):
            if i >= max_samples:
                break
            
            text = example[text_column]
            if text and text.strip():  # Skip empty texts
                texts.append(text.strip())
        
        if verbose:
            print(f"Loaded {len(texts)} texts")
        
        # Train tokenizer
        self.train(texts, vocab_size=vocab_size, verbose=verbose)

    def _apply_bpe(self, word: str) -> List[str]:
        """Apply BPE merges to a word."""
        if word in self.cache:
            return self.cache[word]

        # Split word into characters and add end-of-word marker
        word_tokens = list(word) + ['</w>']

        if len(word_tokens) == 1:
            self.cache[word] = word_tokens
            return word_tokens

        # Apply merges
        for merge in self.merges:
            i = 0
            while i < len(word_tokens) - 1:
                if (word_tokens[i], word_tokens[i + 1]) == merge:
                    # Merge the pair
                    merged = word_tokens[i] + word_tokens[i + 1]
                    word_tokens = word_tokens[:i] + [merged] + word_tokens[i + 2:]
                else:
                    i += 1

        self.cache[word] = word_tokens
        return word_tokens

    def encode(self, text: str, add_special_tokens: bool = True) -> List[int]:
        """Encode text to token IDs."""
        if not text:
            return []

        # Pre-tokenize
        words = self._get_word_tokens(text)

        # Apply BPE to each word
        bpe_tokens = []
        for word in words:
            word_tokens = self._apply_bpe(word)
            bpe_tokens.extend(word_tokens)

        # Convert to IDs
        token_ids = []
        for token in bpe_tokens:
            token_id = self.vocab.get(token, self.unk_token_id)
            token_ids.append(token_id)

        # Add special tokens
        if add_special_tokens:
            token_ids = [self.bos_token_id] + token_ids + [self.eos_token_id]

        return token_ids

    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """Decode token IDs to text."""
        if not token_ids:
            return ""

        # Convert IDs to tokens
        tokens = []
        for token_id in token_ids:
            token = self.inverse_vocab.get(token_id, self.unk_token)

            # Skip special tokens if requested
            if skip_special_tokens and token in self.special_tokens:
                continue

            tokens.append(token)

        # Join tokens and handle end-of-word markers
        text = ''.join(tokens)
        text = text.replace('</w>', ' ')

        # Clean up extra spaces
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def save_pretrained(self, save_directory: str) -> None:
        """Save tokenizer to directory."""
        import os
        os.makedirs(save_directory, exist_ok=True)

        # Save base tokenizer data
        super().save_pretrained(save_directory)

        # Save BPE-specific data
        merges_file = os.path.join(save_directory, "merges.txt")
        with open(merges_file, 'w', encoding='utf-8') as f:
            f.write("#version: 0.2\n")
            for merge in self.merges:
                f.write(f"{merge[0]} {merge[1]}\n")

    @classmethod
    def from_pretrained(cls, load_directory: str) -> 'BPETokenizer':
        """Load tokenizer from directory."""
        import os

        # Load base tokenizer
        tokenizer = super().from_pretrained(load_directory)

        # Load BPE merges
        merges_file = os.path.join(load_directory, "merges.txt")
        merges = []

        with open(merges_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line in lines[1:]:  # Skip version line
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) == 2:
                        merges.append((parts[0], parts[1]))

        tokenizer.merges = merges
        tokenizer.cache = {}

        return tokenizer

    def get_vocab_stats(self) -> Dict[str, int]:
        """Get vocabulary statistics."""
        stats = {
            "total_tokens": len(self.vocab),
            "special_tokens": len(self.special_tokens),
            "regular_tokens": len(self.vocab) - len(self.special_tokens),
            "merges": len(self.merges)
        }
        return stats
